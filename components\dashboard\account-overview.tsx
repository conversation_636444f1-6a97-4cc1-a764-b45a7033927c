"use client"

import { <PERSON><PERSON> } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import {
  TrendingUp,
  DollarSign,
  User,
  ChevronDown,
  HelpCircle,
  MessageSquare,
  ExternalLink,
  Mic,
  Grid,
  Car,
  Clock,
  BarChart3,
  Trophy,
  Zap,
  Target,
  Rocket,
  Loader2,
} from "lucide-react"
import { useState, useEffect } from "react"
import Image from "next/image"
import { useAuth } from "@/contexts/auth-context"

export default function AccountOverview() {
  const [currentImageIndex, setCurrentImageIndex] = useState(0)
  const [activePhase, setActivePhase] = useState("phase1")
  const [orders, setOrders] = useState<any[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const { getOrders, isAuthenticated } = useAuth()

  console.log('AccountOverview mounted - isAuthenticated:', isAuthenticated, 'orders:', orders.length)

  // Fetch orders when component mounts
  useEffect(() => {
    const fetchOrders = async () => {
      console.log('Fetching orders... isAuthenticated:', isAuthenticated)
      if (isAuthenticated) {
        setLoading(true)
        setError(null)
        try {
          console.log('Calling getOrders...')
          const ordersData = await getOrders()
          console.log('Orders received from API:', ordersData)
          setOrders(ordersData)
        } catch (error) {
          console.error('Error fetching orders:', error)
          setError(error instanceof Error ? error.message : 'Failed to fetch orders')
          setOrders([]) // Clear orders on error
        } finally {
          setLoading(false)
        }
      } else {
        console.log('User not authenticated, skipping order fetch')
        setOrders([])
        setError(null)
      }
    }

    fetchOrders()
  }, [isAuthenticated]) // Removed getOrders from dependencies to prevent infinite loop

  const carouselImages = [
    "https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=300&fit=crop",
    "https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=400&h=300&fit=crop",
    "https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=300&fit=crop",
    "https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=400&h=300&fit=crop"
  ]

  const phaseButtons = [
    { id: "phase1", label: "Phase 1", icon: Target, description: "Evaluation Phase" },
    { id: "phase2", label: "Phase 2", icon: Trophy, description: "Verification Phase" },
    { id: "instant", label: "Instant", icon: Zap, description: "Direct Funding" },
    { id: "hft", label: "HFT", icon: Rocket, description: "High Frequency Trading" },
  ]

  return (
    <div className="space-y-8">
      {/* Header Section */}
      <div className="bg-gradient-to-r from-blue-600 via-purple-600 to-blue-800 text-white p-6 rounded-lg">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold mb-2">Accounts</h1>
            <p className="text-blue-100">Unlock your trading potential with FxThrone. Start trading now!</p>
          </div>
          <div className="flex items-center gap-4">
            <Button className="bg-white text-blue-600 hover:bg-gray-100">
              Start Challenge
            </Button>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Phase Buttons */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {phaseButtons.map((phase) => {
              const IconComponent = phase.icon
              return (
                <Button
                  key={phase.id}
                  onClick={() => setActivePhase(phase.id)}
                  className={`h-20 flex flex-col items-center justify-center gap-2 transition-all duration-200 ${
                    activePhase === phase.id
                      ? "bg-blue-600 text-white"
                      : "bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700"
                  }`}
                >
                  <IconComponent className="w-5 h-5" />
                  <div className="text-center">
                    <div className="font-semibold text-sm">{phase.label}</div>
                    <div className="text-xs opacity-75">{phase.description}</div>
                  </div>
                </Button>
              )
            })}
          </div>

          {/* Sale Banner - Thin Moving Animation */}
          <div className="relative overflow-hidden bg-gradient-to-r from-red-500 to-pink-600 text-white rounded-lg">
            <div className="h-12 flex items-center justify-center relative">
              <div className="animate-marquee whitespace-nowrap flex items-center gap-8">
                <span className="text-sm font-semibold">🎉 SPECIAL OFFER - UP TO 70% OFF!</span>
                <span className="text-sm">Use coupon code: <span className="font-bold text-yellow-300">SAVE70</span></span>
                <span className="text-sm">Limited time offer - Don't miss out!</span>
                <span className="text-sm font-semibold">🎉 SPECIAL OFFER - UP TO 70% OFF!</span>
                <span className="text-sm">Use coupon code: <span className="font-bold text-yellow-300">SAVE70</span></span>
                <span className="text-sm">Limited time offer - Don't miss out!</span>
              </div>
            </div>
          </div>

          {/* Orders Section */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <Trophy className="w-6 h-6 text-blue-600 dark:text-blue-400" />
                <h3 className="text-xl font-bold text-gray-900 dark:text-white">Your Trading Orders</h3>
              </div>
              {loading && (
                <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                  <Loader2 className="w-4 h-4 animate-spin" />
                  Loading orders...
                </div>
              )}
            </div>
            
            {loading ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {[1, 2, 3].map((i) => (
                  <div key={i} className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700 animate-pulse">
                    <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded mb-2"></div>
                    <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded mb-1"></div>
                    <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-2/3"></div>
                  </div>
                ))}
              </div>
            ) : orders.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {orders.map((order, index) => (
                  <div key={index} className="order-card bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700 hover:shadow-lg transition-shadow">
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center gap-2">
                        <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center">
                          <Trophy className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                        </div>
                        <div>
                          <h4 className="font-semibold text-gray-900 dark:text-white">Order #{order.order_id}</h4>
                          <p className="text-sm text-gray-600 dark:text-gray-400">{order.username}</p>
                        </div>
                      </div>
                      <Badge 
                        variant={order.status === 'Pending' ? 'secondary' : order.status === 'Active' ? 'default' : 'destructive'}
                        className="text-xs"
                      >
                        {order.status}
                      </Badge>
                    </div>
                    
                    <div className="space-y-3">
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-gray-600 dark:text-gray-400">Account Size:</span>
                        <span className="font-semibold text-gray-900 dark:text-white">${order.account_size}</span>
                      </div>
                      
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-gray-600 dark:text-gray-400">Balance:</span>
                        <span className="font-semibold text-green-600 dark:text-green-400">${order.balance}</span>
                      </div>
                      
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-gray-600 dark:text-gray-400">Challenge Type:</span>
                        <span className="font-semibold text-blue-600 dark:text-blue-400">{order.challenge_type}</span>
                      </div>
                      
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-gray-600 dark:text-gray-400">Created:</span>
                        <span className="text-sm text-gray-500 dark:text-gray-400">
                          {new Date(order.created_at).toLocaleDateString()}
                        </span>
                      </div>
                    </div>
                    
                    <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                      <Button className="w-full bg-blue-600 hover:bg-blue-700 text-white">
                        View Details
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            ) : error ? (
              <div className="bg-white dark:bg-gray-800 rounded-lg p-8 border border-red-200 dark:border-red-700 text-center">
                <div className="w-12 h-12 bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Trophy className="w-6 h-6 text-red-600 dark:text-red-400" />
                </div>
                <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">Failed to Load Orders</h4>
                <p className="text-red-600 dark:text-red-400 mb-4">{error}</p>
                <Button 
                  onClick={() => window.location.reload()} 
                  className="bg-blue-600 hover:bg-blue-700 text-white"
                >
                  Try Again
                </Button>
              </div>
            ) : (
              <div className="bg-white dark:bg-gray-800 rounded-lg p-8 border border-gray-200 dark:border-gray-700 text-center">
                <Trophy className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">No Orders Yet</h4>
                <p className="text-gray-600 dark:text-gray-400 mb-4">Ready to start your trading journey? Create your first challenge and begin your path to success!</p>
                <Button className="bg-blue-600 hover:bg-blue-700 text-white">
                  Start Challenge
                </Button>
              </div>
            )}
          </div>

          {/* Account Info Box */}
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between">
              <div>
                <p className="font-bold text-gray-900 dark:text-white">Free Dashboard Tour | Login: 00000</p>
                <p className="text-gray-600 dark:text-gray-300 mt-1">You are given a demo account to experience the dashboard</p>
              </div>
              <Button className="bg-purple-100 text-purple-700 hover:bg-purple-200 dark:bg-purple-900/30 dark:text-purple-300">
                Dashboard
              </Button>
            </div>
          </div>
        </div>

        {/* Right Sidebar */}
        <div className="space-y-6">
          {/* Action Buttons */}
          <div className="space-y-4">
            <Button className="w-full bg-purple-600 hover:bg-purple-700 text-white h-16 rounded-lg flex items-center justify-between px-4">
              <div className="flex items-center gap-3">
                <HelpCircle className="w-5 h-5" />
                <div className="text-left">
                  <p className="font-semibold">Trading Rules & Guidelines</p>
                </div>
              </div>
              <ExternalLink className="w-4 h-4" />
            </Button>
          </div>

          {/* Image Carousel */}
          <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Gallery</h3>
            <div className="relative">
              <div className="w-full h-80 bg-gray-200 dark:bg-gray-700 rounded-lg overflow-hidden">
                <Image
                  src={carouselImages[currentImageIndex]}
                  alt="Trading platform"
                  width={400}
                  height={320}
                  className="w-full h-full object-cover"
                />
              </div>
              
              {/* Carousel Navigation */}
              <div className="flex justify-center gap-2 mt-4">
                {carouselImages.map((_, index) => (
                  <button
                    key={index}
                    onClick={() => setCurrentImageIndex(index)}
                    className={`w-3 h-3 rounded-full transition-colors ${
                      currentImageIndex === index 
                        ? 'bg-blue-500' 
                        : 'bg-gray-300 dark:bg-gray-600'
                    }`}
                  />
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Custom CSS for marquee animation */}
      <style jsx>{`
        @keyframes marquee {
          0% {
            transform: translateX(100%);
          }
          100% {
            transform: translateX(-100%);
          }
        }
        .animate-marquee {
          animation: marquee 20s linear infinite;
        }
        
        .order-card {
          transition: all 0.3s ease;
        }
        
        .order-card:hover {
          transform: translateY(-2px);
          box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
      `}</style>
    </div>
  )
}
