"use client"

import { useEffect } from 'react'
import { useAuth } from '@/contexts/auth-context'

export default function AuthStatusChecker() {
  const { isAuthenticated, user, logout } = useAuth()

  useEffect(() => {
    const checkAuthStatus = async () => {
      if (!isAuthenticated || !user) {
        return
      }

      const token = localStorage.getItem('auth_token')
      if (!token || token.startsWith('mock_jwt_token_')) {
        return
      }

      try {
        const response = await fetch('https://xthronebackend-9dbecc0e3719.herokuapp.com/auth/user/me', {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        })

        if (response.status === 401) {
          console.log('Token validation failed - logging out user')
          logout()
        }
      } catch (error) {
        console.error('Auth status check failed:', error)
        // Don't logout on network errors, only on 401
      }
    }

    // Check auth status every 5 minutes
    const interval = setInterval(checkAuthStatus, 5 * 60 * 1000)
    
    // Also check immediately
    checkAuthStatus()

    return () => clearInterval(interval)
  }, [isAuthenticated, user, logout])

  // This component doesn't render anything
  return null
} 