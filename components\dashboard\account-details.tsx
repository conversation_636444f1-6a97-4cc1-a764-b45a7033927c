"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { 
  Key, 
  Lock, 
  Server, 
  DollarSign, 
  Monitor, 
  User, 
  Copy,
  Eye,
  EyeOff,
  RefreshCw,
  AlertCircle,
  CheckCircle,
  Clock,
  TrendingUp,
  Shield,
  Settings,
  Download,
  ExternalLink,
  Loader2
} from "lucide-react"
import { toast } from "sonner"
import { useAuth } from "@/contexts/auth-context"

interface AccountCredential {
  order_id: string
  username: string
  status: string
  account_size: string
  challenge_type: string
  created_at: string
  platform: string
  payment_method: string
  completed_at?: string | null
  credentials?: {
    server: string
    platform_login: string
    platform_password: string
  }
}

interface AccountDetailsResponse {
  user: {
    username: string
    email: string
    name: string
  }
  total_orders: number
  orders: AccountCredential[]
}

export default function AccountDetails() {
  const [showPassword, setShowPassword] = useState<{ [key: string]: boolean }>({})
  const [selectedAccount, setSelectedAccount] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [accountData, setAccountData] = useState<AccountDetailsResponse | null>(null)
  const [isLoadingData, setIsLoadingData] = useState(true)
  const { user, isAuthenticated } = useAuth()

  // Fetch account details from backend
  const fetchAccountDetails = async () => {
    try {
      setIsLoadingData(true)
      const token = localStorage.getItem('auth_token')
      
      if (!token) {
        toast.error("Please login to view account details")
        return
      }

      // Check if this is a mock token (development fallback)
      const isMockToken = token.startsWith('mock_jwt_token_')
      
      console.log('=== ACCOUNT DETAILS DEBUG ===')
      console.log('Token found:', token ? 'Yes' : 'No')
      console.log('Full token:', token)
      console.log('Token type:', isMockToken ? 'MOCK' : 'REAL')
      console.log('Token starts with mock_jwt_token_:', token?.startsWith('mock_jwt_token_'))

             if (isMockToken) {
         console.log('Using mock token - returning mock account data for development')
         // Return mock account data for development that matches the real API structure
         const mockAccountData: AccountDetailsResponse = {
           user: {
             username: "Goat",
             email: "<EMAIL>",
             name: "Goat"
           },
           total_orders: 3,
           orders: [
             {
               order_id: "FW46418049",
               username: "Goat",
               status: "Pending",
               account_size: "50000",
               challenge_type: "HFT Pro",
               created_at: "2025-07-31T04:12:14.281052",
               platform: "mt5",
               payment_method: "binance"
             },
             {
               order_id: "FW74651244",
               username: "Goat",
               status: "Pending",
               account_size: "500000",
               challenge_type: "HFT Pro",
               created_at: "2025-07-31T04:13:41.805833",
               platform: "mt5",
               payment_method: "binance"
             },
             {
               order_id: "FW86834714",
               username: "Goat",
               status: "Completed",
               account_size: "500000",
               challenge_type: "HFT",
               created_at: "2025-07-31T04:36:08.991612",
               platform: "MT4",
               payment_method: "Vlasd",
               completed_at: null,
               credentials: {
                 server: "string",
                 platform_login: "string",
                 platform_password: "string"
               }
             }
           ]
         }
         setAccountData(mockAccountData)
         toast.success("Mock account details loaded successfully (Development mode)")
         return
       }

      // Use the real token with Bearer format
      const authHeader = `Bearer ${token}`
      console.log('Using real auth token for account details API call')

      const response = await fetch('https://xthronebackend-9dbecc0e3719.herokuapp.com/order/my-account-details', {
        method: 'GET',
        headers: {
          'Authorization': authHeader,
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) {
        console.error('API Response Status:', response.status)
        const responseText = await response.text()
        console.error('API Response Text:', responseText)

        // If unauthorized, the token might be expired or invalid
        if (response.status === 401) {
          console.error('Authentication failed - token may be expired or invalid')
          // Clear invalid token and redirect to login
          localStorage.removeItem('auth_token')
          localStorage.removeItem('user_data')
          toast.error("Authentication failed. Please login again.")
          return
        }

        throw new Error(`Failed to fetch account details: ${response.status} - ${responseText}`)
      }

      const data: AccountDetailsResponse = await response.json()
      console.log('=== REAL BACKEND DATA FETCHED ===')
      console.log('Account details fetched successfully from API:', data)
      console.log('User:', data.user)
      console.log('Total orders:', data.total_orders)
      console.log('Orders:', data.orders)
      setAccountData(data)
      toast.success("Account details loaded successfully (Real backend data)")
    } catch (error) {
      console.error('Error fetching account details:', error)
      toast.error("Failed to load account details")
    } finally {
      setIsLoadingData(false)
    }
  }

  useEffect(() => {
    if (isAuthenticated) {
      fetchAccountDetails()
    }
  }, [isAuthenticated])

  const togglePasswordVisibility = (orderId: string) => {
    setShowPassword(prev => ({
      ...prev,
      [orderId]: !prev[orderId]
    }))
  }

  const copyToClipboard = (text: string, label: string) => {
    navigator.clipboard.writeText(text)
    toast.success(`${label} copied to clipboard`)
  }

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "completed":
        return "bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400"
      case "pending":
        return "bg-yellow-100 text-yellow-700 dark:bg-yellow-900/30 dark:text-yellow-400"
      case "expired":
        return "bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-400"
      default:
        return "bg-gray-100 text-gray-700 dark:bg-gray-800 dark:text-gray-300"
    }
  }

  const getChallengeTypeColor = (type: string) => {
    switch (type.toLowerCase()) {
      case "hft pro":
        return "bg-purple-100 text-purple-700 dark:bg-purple-900/30 dark:text-purple-400"
      case "hft":
        return "bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-400"
      default:
        return "bg-gray-100 text-gray-700 dark:bg-gray-800 dark:text-gray-300"
    }
  }

  const handleRefreshAccount = async (orderId: string) => {
    setIsLoading(true)
    try {
      await fetchAccountDetails()
      toast.success("Account information refreshed")
    } catch (error) {
      toast.error("Failed to refresh account information")
    } finally {
      setIsLoading(false)
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  if (!isAuthenticated) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <AlertCircle className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">Authentication Required</h3>
          <p className="text-gray-600 dark:text-white/70 mb-4">Please login to view your account details</p>
          <Button onClick={() => window.location.href = '/auth'}>
            Login Now
          </Button>
        </div>
      </div>
    )
  }

  if (isLoadingData) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin text-blue-600 mx-auto mb-4" />
          <p className="text-gray-600 dark:text-white/70">Loading account details...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-500/10 to-cyan-500/10 dark:from-blue-500/20 dark:to-cyan-500/20 backdrop-blur-sm border border-blue-200 dark:border-blue-400/20 rounded-3xl p-8 shadow-xl">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">Account Details</h1>
            <p className="text-lg text-gray-700 dark:text-white/70">
              Welcome back, {accountData?.user?.name || user?.firstName || 'Trader'}
            </p>
          </div>
          <div className="flex items-center gap-3">
            <Badge className="bg-blue-600 dark:bg-blue-500 text-white">
              <Shield className="w-4 h-4 mr-1" />
              Secure Access
            </Badge>
            <Button 
              onClick={fetchAccountDetails}
              disabled={isLoading}
              variant="outline"
              size="sm"
            >
              {isLoading ? (
                <RefreshCw className="w-4 h-4 animate-spin" />
              ) : (
                <RefreshCw className="w-4 h-4" />
              )}
              Refresh
            </Button>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="bg-white/50 dark:bg-white/5 rounded-2xl p-4 border border-gray-200 dark:border-white/10">
            <div className="flex items-center justify-between">
              <div className="w-10 h-10 rounded-xl bg-gradient-to-r from-blue-500 to-cyan-500 flex items-center justify-center shadow-lg">
                <User className="w-5 h-5 text-white" />
              </div>
              <div className="text-right">
                <div className="text-2xl font-bold text-gray-900 dark:text-white">
                  {accountData?.total_orders || 0}
                </div>
                <div className="text-sm text-gray-600 dark:text-white/60">Total Orders</div>
              </div>
            </div>
          </div>

          <div className="bg-white/50 dark:bg-white/5 rounded-2xl p-4 border border-gray-200 dark:border-white/10">
            <div className="flex items-center justify-between">
              <div className="w-10 h-10 rounded-xl bg-gradient-to-r from-green-500 to-emerald-500 flex items-center justify-center shadow-lg">
                <CheckCircle className="w-5 h-5 text-white" />
              </div>
              <div className="text-right">
                <div className="text-2xl font-bold text-gray-900 dark:text-white">
                  {accountData?.orders?.filter(order => order.status.toLowerCase() === 'completed').length || 0}
                </div>
                <div className="text-sm text-gray-600 dark:text-white/60">Completed</div>
              </div>
            </div>
          </div>

          <div className="bg-white/50 dark:bg-white/5 rounded-2xl p-4 border border-gray-200 dark:border-white/10">
            <div className="flex items-center justify-between">
              <div className="w-10 h-10 rounded-xl bg-gradient-to-r from-yellow-500 to-orange-500 flex items-center justify-center shadow-lg">
                <Clock className="w-5 h-5 text-white" />
              </div>
              <div className="text-right">
                <div className="text-2xl font-bold text-gray-900 dark:text-white">
                  {accountData?.orders?.filter(order => order.status.toLowerCase() === 'pending').length || 0}
                </div>
                <div className="text-sm text-gray-600 dark:text-white/60">Pending</div>
              </div>
            </div>
          </div>

          <div className="bg-white/50 dark:bg-white/5 rounded-2xl p-4 border border-gray-200 dark:border-white/10">
            <div className="flex items-center justify-between">
              <div className="w-10 h-10 rounded-xl bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center shadow-lg">
                <DollarSign className="w-5 h-5 text-white" />
              </div>
              <div className="text-right">
                <div className="text-2xl font-bold text-gray-900 dark:text-white">
                  ${accountData?.orders?.reduce((total, order) => {
                    const size = parseInt(order.account_size.replace(/[^0-9]/g, ''))
                    return total + size
                  }, 0).toLocaleString() || '0'}
                </div>
                <div className="text-sm text-gray-600 dark:text-white/60">Total Value</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Account Credentials */}
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Account Credentials</h2>
          <Button className="bg-blue-600 hover:bg-blue-700 text-white">
            <Settings className="w-4 h-4 mr-2" />
            Manage Accounts
          </Button>
        </div>

        {!accountData?.orders || accountData.orders.length === 0 ? (
          <Card className="text-center py-12">
            <CardContent>
              <div className="text-gray-500 dark:text-white/50 mb-4">
                <Key className="w-12 h-12 mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">No Account Credentials Found</h3>
                <p className="text-sm">You haven't created any trading challenges yet.</p>
              </div>
              <Button onClick={() => window.location.href = '/dashboard/new-challenge'}>
                Create New Challenge
              </Button>
            </CardContent>
          </Card>
        ) : (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {accountData.orders.map((order) => (
              <Card 
                key={order.order_id}
                className={`cursor-pointer transition-all duration-300 hover:shadow-lg ${
                  selectedAccount === order.order_id ? 'ring-2 ring-blue-500' : ''
                }`}
                onClick={() => setSelectedAccount(order.order_id)}
              >
                <CardHeader className="pb-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="w-12 h-12 rounded-xl bg-gradient-to-r from-blue-500 to-cyan-500 flex items-center justify-center shadow-lg">
                        <Key className="w-6 h-6 text-white" />
                      </div>
                      <div>
                        <CardTitle className="text-lg">{order.platform}</CardTitle>
                        <CardDescription>Order ID: {order.order_id}</CardDescription>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge className={getStatusColor(order.status)}>
                        {order.status}
                      </Badge>
                      <Badge className={getChallengeTypeColor(order.challenge_type)}>
                        {order.challenge_type}
                      </Badge>
                    </div>
                  </div>
                </CardHeader>

                <CardContent className="space-y-4">
                  {/* Account Size */}
                  <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <div className="flex items-center gap-2">
                      <DollarSign className="w-4 h-4 text-green-600" />
                      <span className="font-medium text-gray-900 dark:text-white">Account Size</span>
                    </div>
                    <span className="font-bold text-green-600">${parseInt(order.account_size).toLocaleString()}</span>
                  </div>

                  {/* Created Date */}
                  <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <div className="flex items-center gap-2">
                      <Clock className="w-4 h-4 text-blue-600" />
                      <span className="font-medium text-gray-900 dark:text-white">Created</span>
                    </div>
                    <span className="text-sm text-gray-600 dark:text-white/70">
                      {formatDate(order.created_at)}
                    </span>
                  </div>

                  {/* Payment Method */}
                  <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <div className="flex items-center gap-2">
                      <TrendingUp className="w-4 h-4 text-purple-600" />
                      <span className="font-medium text-gray-900 dark:text-white">Payment</span>
                    </div>
                    <span className="text-sm text-gray-600 dark:text-white/70 capitalize">
                      {order.payment_method}
                    </span>
                  </div>

                  {/* Credentials Section - Only show if completed and has credentials */}
                  {order.status.toLowerCase() === 'completed' && order.credentials && (
                    <>
                      <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
                        <h4 className="font-semibold text-gray-900 dark:text-white mb-3">Trading Credentials</h4>
                        
                        {/* Login Credentials */}
                        <div className="space-y-3">
                          <div className="flex items-center justify-between">
                            <Label className="text-sm font-medium text-gray-700 dark:text-white/70">Login ID</Label>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => copyToClipboard(order.credentials!.platform_login, "Login ID")}
                              className="h-6 px-2 text-xs"
                            >
                              <Copy className="w-3 h-3 mr-1" />
                              Copy
                            </Button>
                          </div>
                          <div className="flex items-center gap-2">
                            <Input
                              type={showPassword[order.order_id] ? "text" : "password"}
                              value={order.credentials.platform_login}
                              readOnly
                              className="bg-gray-50 dark:bg-gray-800"
                            />
                          </div>
                        </div>

                        {/* Password */}
                        <div className="space-y-3">
                          <div className="flex items-center justify-between">
                            <Label className="text-sm font-medium text-gray-700 dark:text-white/70">Password</Label>
                            <div className="flex items-center gap-1">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => togglePasswordVisibility(order.order_id)}
                                className="h-6 px-2 text-xs"
                              >
                                {showPassword[order.order_id] ? <EyeOff className="w-3 h-3" /> : <Eye className="w-3 h-3" />}
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => copyToClipboard(order.credentials!.platform_password, "Password")}
                                className="h-6 px-2 text-xs"
                              >
                                <Copy className="w-3 h-3" />
                              </Button>
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            <Input
                              type={showPassword[order.order_id] ? "text" : "password"}
                              value={order.credentials.platform_password}
                              readOnly
                              className="bg-gray-50 dark:bg-gray-800"
                            />
                          </div>
                        </div>

                        {/* Server */}
                        <div className="space-y-3">
                          <div className="flex items-center justify-between">
                            <Label className="text-sm font-medium text-gray-700 dark:text-white/70">Server</Label>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => copyToClipboard(order.credentials!.server, "Server")}
                              className="h-6 px-2 text-xs"
                            >
                              <Copy className="w-3 h-3 mr-1" />
                              Copy
                            </Button>
                          </div>
                          <div className="flex items-center gap-2">
                            <Server className="w-4 h-4 text-blue-600" />
                            <Input
                              value={order.credentials.server}
                              readOnly
                              className="bg-gray-50 dark:bg-gray-800"
                            />
                          </div>
                        </div>
                      </div>
                    </>
                  )}

                  {/* Action Buttons */}
                  <div className="flex gap-2 pt-4">
                    <Button
                      variant="outline"
                      size="sm"
                      className="flex-1"
                      onClick={() => handleRefreshAccount(order.order_id)}
                      disabled={isLoading}
                    >
                      {isLoading ? (
                        <RefreshCw className="w-4 h-4 animate-spin" />
                      ) : (
                        <RefreshCw className="w-4 h-4" />
                      )}
                      Refresh
                    </Button>
                    {order.status.toLowerCase() === 'completed' && order.credentials && (
                      <Button size="sm" className="flex-1">
                        <ExternalLink className="w-4 h-4 mr-1" />
                        Connect
                      </Button>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>

      {/* Instructions */}
      <Card className="bg-gradient-to-r from-blue-50 to-cyan-50 dark:from-blue-900/20 dark:to-cyan-900/20 border border-blue-200 dark:border-blue-400/20">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-blue-900 dark:text-blue-100">
            <AlertCircle className="w-5 h-5" />
            Important Information
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="text-sm text-blue-800 dark:text-blue-200">
            <p className="mb-2"><strong>How to get trading credentials:</strong></p>
            <ul className="list-disc list-inside space-y-1 ml-4">
              <li>Create a new challenge with your desired account size</li>
              <li>Complete the payment process</li>
              <li>Wait for order completion (usually within 24 hours)</li>
              <li>Trading credentials will be automatically generated</li>
              <li>Use the credentials to connect to your trading platform</li>
            </ul>
          </div>
          <div className="flex items-center gap-2 text-sm text-blue-700 dark:text-blue-300">
            <Shield className="w-4 h-4" />
            <span>All credentials are encrypted and securely stored</span>
          </div>
        </CardContent>
      </Card>
    </div>
  )
} 