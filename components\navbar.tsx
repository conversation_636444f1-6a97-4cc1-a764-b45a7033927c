"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Moon, Sun, Zap, Users, Award, Globe, User, LogIn, Gift } from "lucide-react"
import { useState, useEffect } from "react"
import Link from "next/link"
import Image from "next/image"
import { usePathname } from "next/navigation"
import { useLanguage } from "@/contexts/language-context"

export default function Navbar() {
  const [isDarkMode, setIsDarkMode] = useState(true)
  const { t } = useLanguage()
  const pathname = usePathname()

  // Check system preference on initial load
  useEffect(() => {
    if (typeof window !== "undefined") {
      const prefersDark = window.matchMedia("(prefers-color-scheme: dark)").matches
      setIsDarkMode(prefersDark)
    }
  }, [])

  const toggleTheme = () => {
    setIsDarkMode(!isDarkMode)
    document.documentElement.classList.toggle("dark")
  }

  // Hide navbar on dashboard pages
  if (pathname?.startsWith('/dashboard')) {
    return null
  }

  return (
    <nav
      className="fixed top-4 left-4 right-4 z-40 flex items-center justify-between bg-transparent backdrop-blur-sm border border-gray-200/30 dark:border-white/10 rounded-2xl px-3 py-2 shadow-sm"
      role="navigation"
      aria-label="Main navigation"
    >
      {/* Logo */}
      <Link href="/" className="flex items-center gap-2 group">
        <div className="w-8 h-8 md:w-10 md:h-10 flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
          <Image
            src="https://res.cloudinary.com/dufcjjaav/image/upload/v1754115839/180_3_3_ygapel.png"
            alt="Forex Throne Logo"
            width={40}
            height={40}
            className="w-full h-full object-contain"
            priority
          />
        </div>
        <div className="hidden sm:block">
          <h1 className="text-lg md:text-xl font-bold text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-300">
            Forex Throne
          </h1>
          <p className="text-xs text-gray-600 dark:text-white/60">Prop Trading Firm</p>
        </div>
      </Link>

      {/* Navigation Links */}
      <div className="flex items-center gap-3 md:gap-6">
        <Button
          variant="ghost"
          onClick={toggleTheme}
          className="text-sm md:text-base font-medium text-gray-700 dark:text-white/90 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100/50 dark:hover:bg-white/10 transition-all duration-300 px-3 py-2 rounded-xl group"
          aria-label="Toggle between light and dark theme"
        >
          <div className="group-hover:rotate-180 transition-transform duration-500">
            {isDarkMode ? <Sun className="h-5 w-5" /> : <Moon className="h-5 w-5" />}
          </div>
        </Button>

        {/* Direct Navigation Links */}
        <Link href="/how-it-works" className="hidden sm:block">
          <Button
            variant="ghost"
            className="text-sm md:text-base font-medium text-gray-700 dark:text-white/90 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100/50 dark:hover:bg-white/10 transition-all duration-300 px-3 py-2 rounded-xl"
          >
            <Zap className="h-4 w-4 mr-2" />
            <span className="hidden lg:inline">How It Works</span>
            <span className="lg:hidden">How</span>
          </Button>
        </Link>

        <Link href="/challenges" className="hidden md:block">
          <Button
            variant="ghost"
            className="text-sm md:text-base font-medium text-gray-700 dark:text-white/90 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100/50 dark:hover:bg-white/10 transition-all duration-300 px-3 py-2 rounded-xl"
          >
            <Award className="h-4 w-4 mr-2" />
            Challenges
          </Button>
        </Link>

        <Link href="/faqs" className="hidden lg:block">
          <Button
            variant="ghost"
            className="text-sm md:text-base font-medium text-gray-700 dark:text-white/90 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100/50 dark:hover:bg-white/10 transition-all duration-300 px-3 py-2 rounded-xl"
          >
            <Globe className="h-4 w-4 mr-2" />
            FAQs
          </Button>
        </Link>

        <Link href="/affiliate" className="hidden xl:block">
          <Button
            variant="ghost"
            className="text-sm md:text-base font-medium text-gray-700 dark:text-white/90 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100/50 dark:hover:bg-white/10 transition-all duration-300 px-3 py-2 rounded-xl"
          >
            <Users className="h-4 w-4 mr-2" />
            Affiliate
          </Button>
        </Link>

        <Link href="/giveaway" className="hidden xl:block">
          <Button
            variant="ghost"
            className="text-sm md:text-base font-medium text-gray-700 dark:text-white/90 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100/50 dark:hover:bg-white/10 transition-all duration-300 px-3 py-2 rounded-xl"
          >
            <Gift className="h-4 w-4 mr-2" />
            Giveaway
          </Button>
        </Link>

        {/* Auth Buttons */}
        <Link href="/auth">
          <Button
            variant="ghost"
            className="text-sm md:text-base font-medium text-gray-700 dark:text-white/90 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100/50 dark:hover:bg-white/10 transition-all duration-300 px-4 py-2 rounded-xl border border-gray-200 dark:border-gray-700"
          >
            <LogIn className="h-4 w-4 mr-2" />
            {t("nav.login")}
          </Button>
        </Link>
        <Link href="/auth?mode=signup">
          <Button className="rounded-xl bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-700 hover:to-cyan-700 text-white px-4 md:px-6 py-2 text-sm md:text-base font-medium hover:scale-105 transition-all duration-300 hover:shadow-xl shadow-lg">
            <User className="h-4 w-4 mr-2" />
            {t("nav.getFunded")}
          </Button>
        </Link>
      </div>
    </nav>
  )
} 